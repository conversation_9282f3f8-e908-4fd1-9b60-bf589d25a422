# backend/train.py
import threading
import time
import os
from backend.core.RLWIDM import model_train_RLWIDM
from backend.core.RLWIDM import data_preprocess_RLWIDM


class TrainingManager:
    """
    训练管理类，封装所有训练相关的后端逻辑，包括线程管理和进度更新。
    """
    def __init__(self):
        self._is_training = False
        self._training_thread = None
        self._update_callback = None
        self._lock = threading.Lock()
        self._stop_event = threading.Event()  # 用于控制训练停止
        self._training_finished = threading.Event()  # 用于通知训练完成
        
    def process_data(self, data_path, model_name):
        """
        处理原始数据，将其分割为训练集和测试集。
        使用异步方式处理，避免阻塞UI。

        Args:
            data_path (str): 原始数据文件路径
            model_name (str): 模型名称

        Returns:
            tuple: (训练集路径, 测试集路径)
        """
        # 使用字典映射模型名称和数据处理函数
        data_processors = {
            "基于强化学习的波形决策模型": data_preprocess_RLWIDM,
        }

        if model_name not in data_processors:
            raise ValueError(f"不支持的模型类型：{model_name}")

        def _async_process():
            """异步数据处理函数"""
            try:
                print(f"开始处理数据: {data_path}, 模型: {model_name}")
                # 获取对应的数据处理函数
                processor = data_processors[model_name]
                train_path, test_path = processor(data_path)
                print(f"数据处理完成: 训练集={train_path}, 测试集={test_path}")
                return train_path, test_path
            except Exception as e:
                print(f"数据处理失败: {str(e)}")
                raise

        # 在新线程中执行数据处理
        import threading
        result = [None, None]  # 用于存储结果
        exception = [None]     # 用于存储异常

        def thread_worker():
            try:
                result[0], result[1] = _async_process()
            except Exception as e:
                exception[0] = e

        # 启动线程
        thread = threading.Thread(target=thread_worker, daemon=True)
        thread.start()
        thread.join()  # 等待线程完成

        # 检查是否有异常
        if exception[0]:
            raise exception[0]

        return result[0], result[1]



    @property
    def is_training(self):
        """获取当前训练状态。"""
        return self._is_training

    def is_training_finished(self):
        """
        检查训练是否已完成。

        Returns:
            bool: True 表示训练已完成，False 表示训练还在进行或未开始
        """
        return self._training_finished.is_set()
    
    def start_training(self, training_params, update_callback):
        """
        启动训练过程。

        Args:
            training_params (dict): 包含所有训练参数的字典。
            update_callback (callable): 用于在训练过程中更新UI的回调函数。
            
        Returns:
            bool: 是否成功启动训练
        """
        with self._lock:
            # 检查是否有正在运行的训练
            if self._is_training or (self._training_thread and self._training_thread.is_alive()):
                print("训练已经在运行中。")
                return False

            # 重置停止事件和完成事件
            self._stop_event.clear()
            self._training_finished.clear()
            self._is_training = True
            self._update_callback = update_callback
            
            try:
                # 在新线程中运行训练核心逻辑
                self._training_thread = threading.Thread(
                    target=self._run_training_core,
                    args=(training_params,),
                    daemon=True
                )
                self._training_thread.start()
                print("训练已启动。")
                return True
                
            except Exception as e:
                self._is_training = False
                self._training_thread = None
                print(f"启动训练失败: {str(e)}")
                if self._update_callback:
                    self._update_callback(0, f"启动训练失败: {str(e)}")
                return False

    def stop_training(self):
        """
        终止训练进程。
        通过设置停止事件来终止训练。
        
        Returns:
            bool: 是否成功发送终止信号
        """
        with self._lock:
            if not self._is_training or not self._training_thread:
                print("没有正在进行的训练。")
                return False

            print("正在终止训练...")
            # 设置停止事件和状态
            self._stop_event.set()
            self._is_training = False
            
            # 通知UI训练已终止
            if self._update_callback:
                self._update_callback(0, "正在终止训练...")

            try:
                # 创建一个新线程来等待训练结束
                def wait_for_training():
                    try:
                        self._training_thread.join(timeout=3.0)  # 给予更长的等待时间
                        if self._training_thread and self._training_thread.is_alive():
                            print("训练线程已被终止")
                        else:
                            print("训练已正常终止")
                        
                        # 确保在主线程中更新UI
                        if self._update_callback:
                            self._update_callback(0, "训练已终止")
                            
                    except Exception as e:
                        print(f"等待训练终止时发生错误: {str(e)}")
                
                # 启动等待线程
                wait_thread = threading.Thread(target=wait_for_training, daemon=True)
                wait_thread.start()
                return True
                    
            except Exception as e:
                print(f"终止训练时发生错误: {str(e)}")
                return False
            
            finally:
                # 确保清理状态
                self._training_thread = None

    def _run_training_core(self, training_params):
        """
        训练核心逻辑。
        
        Args:
            training_params (dict): 包含所有训练参数的字典。
        """
        try:
            print("后端：开始训练。接收参数如下：")
            for key, value in training_params.items():
                print(f"  {key}: {value}")
                
            # 使用字典映射模型名称和训练函数
            model_trainers = {
                "基于强化学习的波形决策模型": model_train_RLWIDM,
            }
            
            model_name = training_params.get('model_name')
            if model_name not in model_trainers:
                raise ValueError(f"不支持的模型类型：{model_name}")
            
            # 为训练函数构造一个进度回调包装器
            def progress_wrapper(progress, message, model_path=None):
                if self._stop_event.is_set():  # 检查是否请求停止
                    return False
                if self._update_callback:
                    self._update_callback(progress, message, model_path)
                return True
            
            # 获取对应的训练函数并执行
            trainer = model_trainers[model_name]
            print("准备开始训练过程...")
            
            if not self._stop_event.is_set():  # 确保训练没有被终止
                print("调用训练函数...")
                trained_model_path = trainer(training_params, progress_wrapper)
                print(f"训练函数返回，model_path: {trained_model_path}")
                
                # 检查训练是否被终止
                if not self._stop_event.is_set() and trained_model_path:
                    print("训练成功完成，更新UI...")
                    if self._update_callback:
                        self._update_callback(100, "训练完成！", trained_model_path)
                else:
                    print(f"训练未完成或被终止。stop_event: {self._stop_event.is_set()}, model_path: {trained_model_path}")
            
        except Exception as e:
            print(f"训练过程发生错误: {str(e)}")
            if self._update_callback:
                self._update_callback(0, f"训练失败: {str(e)}")
            raise
            
        finally:
            self._is_training = False
            if self._stop_event.is_set() and self._update_callback:
                self._update_callback(0, "训练已终止")
            # 设置训练完成事件
            self._training_finished.set()
            print("训练线程已结束")

