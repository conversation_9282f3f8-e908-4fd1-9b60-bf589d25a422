# backend/test.py
import threading
import time

class TestManager:
    """
    测试管理类，封装所有模型测试相关的后端逻辑和线程管理。
    """
    def __init__(self):
        self._test_thread = None
    
    def start_testing(self, model_path, result_callback):
        """
        启动模型测试过程。

        Args:
            model_path (str): 模型文件路径。
            result_callback (callable): 测试完成后用于更新UI的回调函数。
        """
        if self._test_thread and self._test_thread.is_alive():
            print("测试已在运行中。")
            return
        
        # 通知UI“正在测试中...”
        result_callback("测试中...")
        
        # 在新线程中运行测试核心逻辑
        self._test_thread = threading.Thread(
            target=self._run_testing_core,
            args=(model_path, result_callback)
        )
        self._test_thread.daemon = True
        self._test_thread.start()
        print("测试已启动。")
        
    def _run_testing_core(self, model_path, result_callback):
        """
        测试核心逻辑，模拟测试过程。
        """
        print(f"后端：开始测试模型文件: {model_path}")
        try:
            time.sleep(2) # 模拟测试耗时
            result = "测试波形标签：BPSK_1M"
            print("后端：测试完成！")
            
            # 调用前端的回调函数来更新UI结果
            result_callback(result)
            
        except Exception as e:
            result_callback(f"测试出错: {e}")