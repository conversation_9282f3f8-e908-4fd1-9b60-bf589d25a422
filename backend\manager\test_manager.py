# backend/test.py
import threading
from backend.core.RLWIDM.waveDecide_RL import model_test_RLWIDM

class TestManager:
    """
    测试管理类，封装所有模型测试相关的后端逻辑和线程管理。
    """
    def __init__(self):
        self._test_thread = None
    
    def start_testing(self, testing_params, result_callback):
        """
        启动模型测试过程。

        Args:
            testing_params (dict): 包含模型信息和参数的字典。
            result_callback (callable): 测试完成后用于更新UI的回调函数。
        """
        if self._test_thread and self._test_thread.is_alive():
            print("测试已在运行中。")
            return

        # 通知UI“正在测试中...”
        result_callback("测试中...")

        # 在新线程中运行测试核心逻辑
        self._test_thread = threading.Thread(
            target=self._run_testing_core,
            args=(testing_params, result_callback)
        )
        self._test_thread.daemon = True
        self._test_thread.start()
        print("测试已启动。")
        
    def _run_testing_core(self, testing_params, result_callback):
        """
        测试核心逻辑，调用真实的模型测试函数。
        """
        model_name = testing_params["model_name"]
        model_path = testing_params["model_path"]
        parameters = testing_params["parameters"]

        print(f"后端：开始测试模型: {model_name}，模型文件: {model_path}")
        print(f"测试参数: {parameters}")

        # 使用字典映射模型名称和测试函数
        model_testers = {
            "基于强化学习的波形决策模型": model_test_RLWIDM,
        }

        if model_name not in model_testers:
            error_msg = f"不支持的模型类型：{model_name}"
            print(f"后端：{error_msg}")
            result_callback(error_msg)
            return

        try:
            # 获取对应的测试函数并执行
            tester = model_testers[model_name]
            print(f"调用测试函数: {tester.__name__}")

            # 调用真实的模型测试函数
            result = tester(model_path, parameters)

            if result is not None:
                print(f"后端：测试完成！结果: {result}")
                # 调用前端的回调函数来更新UI结果
                result_callback(result)
            else:
                error_msg = "测试失败：未能获取有效的测试结果"
                print(f"后端：{error_msg}")
                result_callback(error_msg)

        except Exception as e:
            error_msg = f"测试出错: {str(e)}"
            print(f"后端：{error_msg}")
            result_callback(error_msg)