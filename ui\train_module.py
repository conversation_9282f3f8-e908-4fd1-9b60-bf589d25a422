# type: ignore
"""
训练模块

展示模型训练的窗口
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from backend.manager import TrainingManager # 导入后端训练管理类

class TrainModule:
    """
    模型训练模块的UI逻辑类。
    """
    def __init__(self, parent_frame, progress_bar_module, select_file_callback):
        self.parent_frame = parent_frame
        self.progress_bar_module = progress_bar_module
        self.select_file_callback = select_file_callback
        self.training_manager = TrainingManager() # 实例化后端管理类
        self.current_page = 0
        self.pages = []
        self.scroll_canvases = []  # 存储所有页面的画布
        
        # 存储UI控件的引用
        # 模型选择页面
        self.model_combo = None
        
        # 数据处理页面
        self.path_entry = None
        self.train_path_entry = None
        self.validation_path_entry = None
        
        # 训练完成后的模型路径
        self.trained_model_path_entry = None
        
        # 参数配置页面
        self.rounds_entry = None  # 训练回合数
        self.batch_entry = None   # 批次大小
        self.seed_entry = None    # 随机种子
        self.optimizer_combo = None  # 优化器
        self.lr_entry = None      # 学习率
        
        self.create_pages()
        self.create_nav_buttons()
        self.show_page(self.current_page)
        
    def create_scrollable_frame(self, parent):
        """
        创建一个带滚动条的框架。
        
        Args:
            parent: 父容器
            
        Returns:
            tuple: (content_frame, canvas) 内容框架和画布
        """
        # 创建画布和滚动条
        canvas = tk.Canvas(parent, bg="white", highlightthickness=0)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        
        # 创建内容框架
        content_frame = tk.Frame(canvas, bg="white")
        
        # 配置画布的滚动区域
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 放置画布和滚动条
        scrollbar.pack(side="right", fill="y", padx=(0, 2))
        canvas.pack(side="left", fill="both", expand=True, padx=(10, 2))
        
        # 创建画布窗口
        window = canvas.create_window((0, 0), window=content_frame, anchor="nw")
        
        # 配置画布滚动范围
        def configure_scroll_region(event=None):
            canvas.configure(scrollregion=canvas.bbox("all"))
            # 设置内容框架的宽度，确保至少有400像素宽
            min_width = max(400, canvas.winfo_width())
            canvas.itemconfig(window, width=min_width)
        
        content_frame.bind("<Configure>", configure_scroll_region)
        canvas.bind("<Configure>", configure_scroll_region)
        
        # 绑定鼠标滚轮事件
        def on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
        canvas.bind_all("<MouseWheel>", on_mousewheel)
        self.scroll_canvases.append((canvas, configure_scroll_region))  # 保存画布和更新函数的引用
        
        return content_frame

    def create_pages(self):
        """
        创建训练模块的四个分页。
        """
        # 设置按钮样式
        style = ttk.Style()
        style.configure('Custom.TButton', 
                      font=('Microsoft YaHei', 10),
                      padding=5,
                      width=15)
        
        # 第一页：模型选择
        page1 = tk.Frame(self.parent_frame, bg="white")
        self.pages.append(page1)  # 先添加到页面列表
        
        content1 = self.create_scrollable_frame(page1)
        
        title1 = tk.Label(content1, text="选择训练模型", 
                         font=("Microsoft YaHei", 14, "bold"),
                         bg="white", fg="#2c3e50")
        title1.pack(pady=(20, 15))
        
        model_frame = tk.Frame(content1, bg="white")
        model_frame.pack(fill="x", padx=20)
        
        # 基本模型设置
        basic_frame = tk.Frame(model_frame, bg="white")
        basic_frame.pack(fill="x", pady=(0, 15))
        
        tk.Label(basic_frame, text="基本设置：",
                font=("Microsoft YaHei", 11, "bold"),
                bg="white", fg="#2c3e50").pack(anchor="w", pady=(0, 10))
                
        tk.Label(basic_frame, text="选择模型类型：",
                font=("Microsoft YaHei", 10),
                bg="white", fg="#34495e").pack(anchor="w", pady=(0, 5))
        
        models = [
            "基于深度神经网络的波形智能决策模型",
            "基于强化学习的波形决策模型", 
            "基于决策树的MAC协议优选模型",
            "基于模糊推理神经网络的路由协议优选模型",
            "基于贝叶斯网络的路由协议参数决策模型",
            "基于强化学习的路由参数决策模型"]
        self.model_combo = ttk.Combobox(basic_frame, values=models,
                                       font=("Microsoft YaHei", 10),
                                       state="readonly", width=35)
        self.model_combo.set(models[0])
        self.model_combo.pack(fill="x")
        
        # # 高级设置
        # advanced_frame = tk.Frame(model_frame, bg="white")
        # advanced_frame.pack(fill="x", pady=(15, 0))
        
        # tk.Label(advanced_frame, text="高级设置：",
        #         font=("Microsoft YaHei", 11, "bold"),
        #         bg="white", fg="#2c3e50").pack(anchor="w", pady=(0, 10))
        
        # options = [
        #     "使用预训练模型",
        #     "启用模型量化",
        #     "启用梯度裁剪",
        #     "使用混合精度训练"
        # ]
        
        # for option in options:
        #     var = tk.BooleanVar()
        #     cb = ttk.Checkbutton(advanced_frame, text=option, variable=var)
        #     cb.pack(anchor="w", pady=2)
        
        # 第二页：数据处理
        page2 = tk.Frame(self.parent_frame, bg="white")
        self.pages.append(page2)  # 先添加到页面列表
        
        content2 = self.create_scrollable_frame(page2)
        
        title2 = tk.Label(content2, text="数据处理",
                         font=("Microsoft YaHei", 14, "bold"),
                         bg="white", fg="#2c3e50")
        title2.pack(pady=(20, 15))
        
        # 数据源设置
        source_frame = tk.Frame(content2, bg="white")
        source_frame.pack(fill="x")
        
        tk.Label(source_frame, text="数据源设置：",
                font=("Microsoft YaHei", 11, "bold"),
                bg="white", fg="#2c3e50").pack(anchor="w", pady=(0, 10))
        
        # 原始数据路径
        tk.Label(source_frame, text="原始数据路径：",
                font=("Microsoft YaHei", 10),
                bg="white", fg="#34495e").pack(anchor="w", pady=(0, 5))
        
        path_frame = tk.Frame(source_frame, bg="white")
        path_frame.pack(fill="x", pady=(0, 15))
        self.path_entry = tk.Entry(path_frame, font=("Microsoft YaHei", 10), relief="solid", borderwidth=1)
        self.path_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))
        
        open_button = ttk.Button(path_frame, text="浏览",
                               style='Custom.TButton',
                               command=lambda: self.select_file_callback(self.path_entry))
        open_button.pack(side="right", padx=(10, 0))
        
        # 处理后的训练集路径
        tk.Label(source_frame, text="处理后的训练集路径：",
                font=("Microsoft YaHei", 10),
                bg="white", fg="#34495e").pack(anchor="w", pady=(10, 5))
                
        train_path_frame = tk.Frame(source_frame, bg="white")
        train_path_frame.pack(fill="x", pady=(0, 15))
        self.train_path_entry = tk.Entry(train_path_frame, font=("Microsoft YaHei", 10), relief="solid", borderwidth=1)
        self.train_path_entry.pack(side="left", fill="x", expand=True)
        self.train_path_entry.configure(state='readonly')
        
        # 处理后的验证集路径
        tk.Label(source_frame, text="处理后的验证集路径：",
                font=("Microsoft YaHei", 10),
                bg="white", fg="#34495e").pack(anchor="w", pady=(10, 5))
                
        validation_path_frame = tk.Frame(source_frame, bg="white")
        validation_path_frame.pack(fill="x", pady=(0, 15))
        self.validation_path_entry = tk.Entry(validation_path_frame, font=("Microsoft YaHei", 10), relief="solid", borderwidth=1)
        self.validation_path_entry.pack(side="left", fill="x", expand=True)
        self.validation_path_entry.configure(state='readonly')
        
        # # 数据预处理选项
        # preprocess_frame = tk.Frame(content2, bg="white")
        # preprocess_frame.pack(fill="x", pady=(15, 0))
        
        # tk.Label(preprocess_frame, text="数据预处理：",
        #         font=("Microsoft YaHei", 11, "bold"),
        #         bg="white", fg="#2c3e50").pack(anchor="w", pady=(0, 10))
        
        # options = ["标准化处理", "归一化处理", "数据增强", "噪声过滤", "异常值处理"]
        # for option in options:
        #     var = tk.BooleanVar()
        #     cb = ttk.Checkbutton(preprocess_frame, text=option, variable=var)
        #     cb.pack(anchor="w", pady=2)
        
        # # 数据划分设置
        # split_frame = tk.Frame(content2, bg="white")
        # split_frame.pack(fill="x", pady=(10, 0))  # 移除内边距，保持与其他部分一致
        
        # tk.Label(split_frame, text="数据划分：",
        #         font=("Microsoft YaHei", 11, "bold"),
        #         bg="white", fg="#2c3e50").pack(anchor="w", pady=(0, 5))  # 减少下边距
        
        # splits = {
        #     "训练集比例：": "0.7",
        #     "验证集比例：": "0.15",
        #     "验证集比例：": "0.15"
        # }
        
        # splits_container = tk.Frame(split_frame, bg="white")  # 添加一个容器来统一管理输入项
        # splits_container.pack(fill="x")
        
        # for label, default in splits.items():
        #     row = tk.Frame(splits_container, bg="white")
        #     row.pack(fill="x", pady=5)
        #     tk.Label(row, text=label,
        #             font=("Microsoft YaHei", 10),
        #             width=12, bg="white", fg="#34495e",
        #             anchor="e").pack(side="left", padx=(0, 10))  # 统一设置为10像素的间距
        #     entry = tk.Entry(row, font=("Microsoft YaHei", 10), relief="solid", borderwidth=1)
        #     entry.insert(0, default)
        #     entry.pack(side="left", fill="x", expand=True)
        
        # 数据处理按钮框架
        process_button_frame = tk.Frame(content2, bg="white")
        process_button_frame.pack(pady=20)

        self.process_button = ttk.Button(process_button_frame, text="开始处理数据",
                                       style='Custom.TButton',
                                       command=self._process_data)
        self.process_button.pack(side="left", padx=(0, 10))

        self.stop_process_button = ttk.Button(process_button_frame, text="停止处理",
                                            style='Custom.TButton',
                                            command=self._stop_data_processing,
                                            state='disabled')
        self.stop_process_button.pack(side="left")

        # 第三页：参数输入
        page3 = tk.Frame(self.parent_frame, bg="white")
        self.pages.append(page3)  # 添加第三页到列表中
        content3 = self.create_scrollable_frame(page3)
        
        title3 = tk.Label(content3, text="参数配置",
                         font=("Microsoft YaHei", 14, "bold"),
                         bg="white", fg="#2c3e50")
        title3.pack(pady=(20, 15))
        
        # 基础训练参数
        basic_param_frame = tk.Frame(content3, bg="white")
        basic_param_frame.pack(fill="x", padx=20)  # 添加水平内边距
        
        tk.Label(basic_param_frame, text="基础训练参数：",
                font=("Microsoft YaHei", 11, "bold"),
                bg="white", fg="#2c3e50").pack(anchor="w", pady=(0, 5))  # 减少下边距
        
        params = {
            "训练回合数：": "300",
            "批次大小：": "64",
            # "验证频率：": "5",
            # "早停容忍度：": "5",
            "随机种子：": "42"
        }
        
        for label, default in params.items():
            row = tk.Frame(basic_param_frame, bg="white")
            row.pack(fill="x", pady=5)
            tk.Label(row, text=label,
                    font=("Microsoft YaHei", 10),
                    width=12, bg="white", fg="#34495e",
                    anchor="e").pack(side="left", padx=(0, 10))
            if label == "训练回合数：":
                self.rounds_entry = tk.Entry(row, font=("Microsoft YaHei", 10), relief="solid", borderwidth=1)
                self.rounds_entry.insert(0, default)
                self.rounds_entry.pack(side="left", fill="x", expand=True)
            elif label == "批次大小：":
                self.batch_entry = tk.Entry(row, font=("Microsoft YaHei", 10), relief="solid", borderwidth=1)
                self.batch_entry.insert(0, default)
                self.batch_entry.pack(side="left", fill="x", expand=True)
            elif label == "随机种子：":
                self.seed_entry = tk.Entry(row, font=("Microsoft YaHei", 10), relief="solid", borderwidth=1)
                self.seed_entry.insert(0, default)
                self.seed_entry.pack(side="left", fill="x", expand=True)
            else:
                entry = tk.Entry(row, font=("Microsoft YaHei", 10), relief="solid", borderwidth=1)
                entry.insert(0, default)
                entry.pack(side="left", fill="x", expand=True)
        
        # 优化器设置
        optimizer_frame = tk.Frame(content3, bg="white")
        optimizer_frame.pack(fill="x", pady=(10, 0), padx=20)  # 添加水平内边距，减少上边距
        
        tk.Label(optimizer_frame, text="优化器设置：",
                font=("Microsoft YaHei", 11, "bold"),
                bg="white", fg="#2c3e50").pack(anchor="w", pady=(0, 5))  # 减少下边距
        
        optimizer_row = tk.Frame(optimizer_frame, bg="white")
        optimizer_row.pack(fill="x", pady=5)
        
        tk.Label(optimizer_row, text="选择优化器：",
                font=("Microsoft YaHei", 10),
                width=12, bg="white", fg="#34495e",
                anchor="e").pack(side="left", padx=(0, 10))
        
        optimizers = ["Adam", "SGDM", "Nadam"]
        self.optimizer_combo = ttk.Combobox(optimizer_row, values=optimizers,
                                          font=("Microsoft YaHei", 10),
                                          state="readonly")
        self.optimizer_combo.set(optimizers[0])
        self.optimizer_combo.pack(side="left", fill="x", expand=True)
        
        # 学习率设置
        lr_frame = tk.Frame(content3, bg="white")
        lr_frame.pack(fill="x", pady=(10, 0), padx=20)  # 添加水平内边距，减少上边距
        
        tk.Label(lr_frame, text="学习率设置：",
                font=("Microsoft YaHei", 11, "bold"),
                bg="white", fg="#2c3e50").pack(anchor="w", pady=(0, 5))  # 减少下边距
        
        lr_params = {
            "初始学习率：": "0.003",
            # "最小学习率：": "0.00001",
            # "学习率衰减率：": "0.1",
            # "衰减步长：": "10"
        }
        
        for label, default in lr_params.items():
            row = tk.Frame(lr_frame, bg="white")
            row.pack(fill="x", pady=5)
            tk.Label(row, text=label,
                    font=("Microsoft YaHei", 10),
                    width=12, bg="white", fg="#34495e",
                    anchor="e").pack(side="left", padx=(0, 10))
            self.lr_entry = tk.Entry(row, font=("Microsoft YaHei", 10), relief="solid", borderwidth=1)
            self.lr_entry.insert(0, default)
            self.lr_entry.pack(side="left", fill="x", expand=True)

        # 第四页：开始/终止训练
        page4 = tk.Frame(self.parent_frame, bg="white")
        content4 = self.create_scrollable_frame(page4)
        
        title4 = tk.Label(content4, text="训练控制",
                         font=("Microsoft YaHei", 14, "bold"),
                         bg="white", fg="#2c3e50")
        title4.pack(pady=(20, 15))
        
        # # 训练配置
        # config_frame = tk.Frame(content4, bg="white")
        # config_frame.pack(fill="x")
        
        # tk.Label(config_frame, text="训练配置：",
        #         font=("Microsoft YaHei", 11, "bold"),
        #         bg="white", fg="#2c3e50").pack(anchor="w", pady=(0, 10))
        
        # options = [
        #     "启用自动混合精度",
        #     "启用梯度累积",
        #     "启用模型检查点",
        #     "启用TensorBoard记录",
        #     "启用早停机制",
        #     "保存训练日志",
        #     "保存最佳模型"
        # ]
        
        # for option in options:
        #     var = tk.BooleanVar()
        #     cb = ttk.Checkbutton(config_frame, text=option, variable=var)
        #     cb.pack(anchor="w", pady=2)
        
        # 训练完成的模型路径显示
        model_path_frame = tk.Frame(content4, bg="white")
        model_path_frame.pack(fill="x", pady=(0, 20))
        
        tk.Label(model_path_frame, text="训练完成的模型路径：",
                font=("Microsoft YaHei", 10),
                bg="white", fg="#34495e").pack(anchor="w", pady=(0, 5))
                
        self.trained_model_path_entry = tk.Entry(model_path_frame, font=("Microsoft YaHei", 10),
                                                relief="solid", borderwidth=1)
        self.trained_model_path_entry.pack(fill="x", expand=True)
        self.trained_model_path_entry.configure(state='readonly')
        
        # 按钮控制区
        btn_frame = tk.Frame(content4, bg="white")
        btn_frame.pack(fill="x", pady=20)
        
        start_button = ttk.Button(btn_frame, text="开始训练",
                                style='Custom.TButton',
                                command=self._start_training)
        start_button.pack(pady=(0, 10))
        
        stop_button = ttk.Button(btn_frame, text="终止训练",
                               style='Custom.TButton',
                               command=self.training_manager.stop_training)
        stop_button.pack()
        
        self.pages.append(page4)
    
    def create_nav_buttons(self):
        """
        创建并布局页面的导航按钮（上一页/下一页）。
        """
        nav_frame = tk.Frame(self.parent_frame, bg="white")
        nav_frame.pack(side="bottom", pady=15, padx=40, fill="x")
        
        # 创建按钮样式
        style = ttk.Style()
        style.configure('Nav.TButton', 
                      font=('Microsoft YaHei', 10),
                      padding=5,
                      width=10)
        
        # 创建进度指示器
        self.progress_dots = []
        dots_frame = tk.Frame(nav_frame, bg="white")
        dots_frame.pack(side="top", pady=(0, 10))
        
        for i in range(4):
            dot = tk.Label(dots_frame, text="●", font=("Arial", 12),
                         bg="white", fg="#bdc3c7")
            dot.pack(side="left", padx=5)
            self.progress_dots.append(dot)
        
        # 创建按钮容器
        button_frame = tk.Frame(nav_frame, bg="white")
        button_frame.pack(fill="x")
        
        self.prev_button = ttk.Button(button_frame, text="上一步",
                                   style='Nav.TButton', command=self.prev_page)
        self.prev_button.pack(side="left")

        self.next_button = ttk.Button(button_frame, text="下一步",
                                   style='Nav.TButton', command=self.next_page)
        self.next_button.pack(side="right")

    def show_page(self, page_index):
        """
        根据索引显示指定的分页。
        """
        if not (0 <= page_index < len(self.pages)):
            return
        
        # 解绑所有画布的鼠标滚轮事件
        for canvas_tuple in self.scroll_canvases:
            canvas_tuple[0].unbind_all("<MouseWheel>")
        
        # 隐藏所有页面
        for page in self.pages:
            page.pack_forget()
        
        # 显示当前页面
        self.pages[page_index].pack(expand=True, fill="both")
        self.current_page = page_index
        
        # 绑定当前页面的鼠标滚轮事件并更新滚动区域
        if page_index < len(self.scroll_canvases):
            canvas, update_func = self.scroll_canvases[page_index]
            def on_mousewheel(event):
                canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            canvas.bind_all("<MouseWheel>", on_mousewheel)
            # 等待页面完全显示后更新滚动区域
            self.parent_frame.after(100, lambda: (
                self.parent_frame.update_idletasks(),
                update_func()
            ))

        # 更新按钮状态
        self.prev_button["state"] = "normal" if self.current_page > 0 else "disabled"
        self.next_button["state"] = "normal" if self.current_page < len(self.pages) - 1 else "disabled"
        
        # 更新进度指示器
        for i, dot in enumerate(self.progress_dots):
            if i == page_index:
                dot.configure(fg="#3498db", font=("Arial", 14))  # 当前页面
            elif i < page_index:
                dot.configure(fg="#2ecc71", font=("Arial", 12))  # 已完成页面
            else:
                dot.configure(fg="#bdc3c7", font=("Arial", 12))  # 未完成页面

    def next_page(self):
        """显示下一页。"""
        self.show_page(self.current_page + 1)

    def prev_page(self):
        """显示上一页。"""
        self.show_page(self.current_page - 1)
        
    def _process_data(self):
        """
        处理数据的回调函数
        """
        # 检查原始数据路径是否存在
        if not self.path_entry.get():
            tk.messagebox.showerror("错误", "请选择原始数据路径！")
            return

        # 检查是否已经在处理数据
        if self.training_manager.is_processing_data:
            tk.messagebox.showwarning("警告", "数据处理正在进行中，请等待完成！")
            return

        # 启动异步数据处理
        def data_processing_callback(progress, message, train_path=None, test_path=None):
            """数据处理进度回调函数"""
            self.progress_bar_module.update_progress(progress, message)

            # 如果数据处理完成，更新路径输入框
            if progress == 100 and train_path and test_path:
                self.train_path_entry.configure(state='normal')
                self.validation_path_entry.configure(state='normal')

                self.train_path_entry.delete(0, tk.END)
                self.validation_path_entry.delete(0, tk.END)

                self.train_path_entry.insert(0, train_path)
                self.validation_path_entry.insert(0, test_path)

                self.train_path_entry.configure(state='readonly')
                self.validation_path_entry.configure(state='readonly')

                tk.messagebox.showinfo("提示", "数据处理完成！")

        # 启动异步数据处理
        success = self.training_manager.start_data_processing(
            self.path_entry.get(),
            data_processing_callback
        )

        if not success:
            tk.messagebox.showerror("错误", "启动数据处理失败！")

    def _get_training_params(self):
        """
        统一获取UI界面的所有训练参数。
        
        Returns:
            dict: 包含所有训练参数的字典。
        """
        return {
            # 模型信息
            "model_name": self.model_combo.get(),
            
            # 数据路径
            "raw_data_path": self.path_entry.get(),
            "train_data_path": self.train_path_entry.get(),
            "validation_data_path": self.validation_path_entry.get(),
            
            # 训练参数
            "epochs": int(self.rounds_entry.get()),
            "batch_size": int(self.batch_entry.get()),
            "random_seed": int(self.seed_entry.get()) if self.seed_entry.get() else 42,
            
            # 优化器和学习率
            "optimizer": self.optimizer_combo.get(),
            "learning_rate": float(self.lr_entry.get()) if self.lr_entry.get() else 0.001
        }
        
    def _start_training(self):
        """
        UI触发函数，检查参数后调用后端训练管理类的启动方法。
        执行参数验证，并在验证通过后开始训练。
        """
        try:
            training_params = self._get_training_params()
            # 参数完整性检查
            required_paths = ["train_data_path", "validation_data_path"]
            if not all(training_params.get(path) for path in required_paths):
                tk.messagebox.showerror("错误", "请先处理数据，获取训练集和验证集路径！")
                return
            # 参数有效性检查
            if training_params["epochs"] <= 0:
                tk.messagebox.showerror("错误", "训练回合数必须大于0！")
                return
            if training_params["batch_size"] <= 0:
                tk.messagebox.showerror("错误", "批次大小必须大于0！")
                return
            if training_params["learning_rate"] <= 0:
                tk.messagebox.showerror("错误", "学习率必须大于0！")
                return
            # 包装一个进度回调，训练完成时显示模型路径
            def training_progress_callback(percent, text, model_path=None):
                self.progress_bar_module.update_progress(percent, text)
                if text == "训练完成！" and model_path:
                    self.trained_model_path_entry.configure(state='normal')
                    self.trained_model_path_entry.delete(0, tk.END)
                    self.trained_model_path_entry.insert(0, model_path)
                    self.trained_model_path_entry.configure(state='readonly')
            try:
                self.training_manager.start_training(
                    training_params,
                    training_progress_callback
                )
            except Exception as e:
                tk.messagebox.showerror("错误", f"训练失败 - {str(e)}")
                return
        except ValueError as e:
            tk.messagebox.showerror("错误", "参数格式错误：请检查数值参数是否正确！")
            return
        except Exception as e:
            tk.messagebox.showerror("错误", f"启动训练失败：{str(e)}")
            return
