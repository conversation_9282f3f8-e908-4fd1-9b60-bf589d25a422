import os
import time

def model_train_RLWIDM(training_params, progress_callback):
    """
    强化学习波形决策模型的训练函数
    
    Args:
        training_params (dict): 训练参数
        progress_callback (callable): 进度回调函数。返回False表示应该停止训练。
            函数格式：callback(progress, message, model_path=None) -> bool
    
    Returns:
        str: 训练完成后的模型保存路径，如果训练被终止返回None
    """
    try:
        # 训练开始
        progress_callback(0, "开始训练...")
        
        # 构造模型保存路径
        model_name = training_params['model_name'].replace(' ', '_')
        output_dir = os.path.dirname(training_params['train_data_path'])
        model_path = os.path.join(output_dir, f"{model_name}_trained.pt")
        
        # 模拟训练过程
        total_epochs = training_params.get('epochs', 100)
        for epoch in range(total_epochs):
            # 更新进度并检查是否需要终止
            progress = int((epoch + 1) * 100 / total_epochs)
            
            # 将长时间等待分割成多个短暂等待，以便更快响应终止请求
            for _ in range(10):  # 将0.1秒分成10份
                if not progress_callback(progress, f"训练中...第 {epoch+1}/{total_epochs} 轮"):
                    print("训练被终止")
                    return None
                time.sleep(0.01)  # 每次只等待0.01秒
        
        # 再次检查是否需要终止
        if not progress_callback(95, "正在保存模型..."):
            print("训练被终止")
            return None
            
        # 训练完成，保存最终模型
        try:
            with open(model_path, 'w') as f:
                f.write("模拟模型文件内容")
        except Exception as e:
            progress_callback(0, f"保存模型失败：{str(e)}")
            raise
            
        return model_path
        
    except Exception as e:
        progress_callback(0, f"训练失败：{str(e)}")
        raise