import pandas as pd
import sqlite3
import os
import numpy as np
from sklearn.model_selection import train_test_split # 用于划分组ID
import datetime

import sys
from pathlib import Path

# 添加 RLWIDM 目录到 Python 路径
rlwidm_path = Path(__file__).resolve().parent
if str(rlwidm_path) not in sys.path:
    sys.path.append(str(rlwidm_path))

def data_preprocess_RLWIDM(data_path):
    # --- 配置信息 ---
    DATA_DIR = data_path  # 存放所有CSV文件的文件夹路径 (请替换为实际路径)
    CHUNK_SIZE = 10000              # 分块读取大小（根据内存调整）
    CREATE_ALL_TABLE_SQL = """
    CREATE TABLE IF NOT EXISTS all_table (
        feature1 REAL,
        feature2 REAL,
        feature3 REAL,
        feature4 REAL,
        feature5 REAL,
        feature6 REAL,
        action REAL,
        performance REAL,
        group_id TEXT  -- 添加 group_id 列，用于唯一标识特征组
    );
    """

    SEED = 42  # 随机种子，用于确保每次运行的结果一致

    time = datetime.now().strftime("%Y%m%d-%H%M%S")
    log_dir = os.path.join(DATA_DIR, 'logs', f'data-seed{SEED}-{time}')

    ALL_DB_PATH = os.path.join(log_dir, 'all_data.db')         # 主数据库文件名，包含所有原始数据
    ALL_TABLE_NAME = 'all_table'           # 数据库中的主表名

    TRAIN_DB_PATH = os.path.join(log_dir,'train_data.db')     # 训练集数据库文件名
    TEST_DB_PATH = os.path.join(log_dir,'test_data.db')       # 测试集数据库文件名
    TRAIN_TABLE_NAME = 'train_table'                             # 训练集数据库中的表名
    TEST_TABLE_NAME = 'test_table'                               # 测试集数据库中的表名

    CREATE_TRAIN_TABLE_SQL = CREATE_ALL_TABLE_SQL.replace('all_table', TRAIN_TABLE_NAME)
    CREATE_TEST_TABLE_SQL = CREATE_ALL_TABLE_SQL.replace('all_table', TEST_TABLE_NAME)

    CHUNK_SIZE_QUERY = 10 # 分组时每回合处理标签的个数,建议将此值设置为小于或等于数据库IN子句的实际限制




    # 定义组成一个“特征组”的列。这些列的组合唯一标识一个组。
    feature_for_group = ['feature1', 'feature2', 'feature3', 'feature4', 'feature5', 'feature6']

    # 定义所有你需要保存到数据库中的列，包括特征、目标、奖励以及其他任何你后续可能需要的列。
    all_feature = feature_for_group + ['action', 'performance']

    # 添加数据的分组ID信息
    feature_and_id = all_feature + ['group_id']

    # 检查 CSV 文件目录
    if not os.path.isdir(DATA_DIR):
        print(f"错误：指定的 CSV 目录 '{DATA_DIR}' 不存在。请检查路径。")
        exit()

    csv_files = [f for f in os.listdir(DATA_DIR) if f.endswith('.csv')]

    if not csv_files:
        print(f"错误：目录 '{DATA_DIR}' 中没有找到任何 CSV 文件。")
        exit()

    # 删除已存在的数据库文件
    if os.path.exists(ALL_DB_PATH) and os.path.getsize(ALL_DB_PATH) > 0:
        print(f"警告：'{ALL_DB_PATH}' 已存在且非空，将被覆盖。")
        try:
            os.remove(ALL_DB_PATH)
            print(f"已删除旧数据库文件: {ALL_DB_PATH}")
        except Exception as e:
            print(f"删除数据库文件失败: {str(e)}")
            exit()






    print(f"\n --- 步骤 1: 导入 {len(csv_files)} 个 CSV 文件到主数据库 ---")
    print(f"目标数据库: {ALL_DB_PATH}, 表: {ALL_TABLE_NAME}")

    # --- 连接或创建主数据库 ---
    conn_all = sqlite3.connect(ALL_DB_PATH)
    cursor_all = conn_all.cursor()
    # 创建主表（如果不存在）
    cursor_all.execute(CREATE_ALL_TABLE_SQL)
    # 提交创建表的操作
    conn_all.commit()

    # 遍历CSV文件并导入
    for i, file_name in enumerate(csv_files):
        file_path = os.path.join(DATA_DIR, file_name)
        try:
            # 使用 chunksize 分块读取大型 CSV 文件，避免内存问题
            for chunk in pd.read_csv(
                file_path, 
                chunksize = CHUNK_SIZE,
                header = 0,
                names = [
                    'feature1',
                    'feature2',
                    'feature3',
                    'feature4',
                    'feature5',
                    'feature6',
                    'action',
                    'performance'
                ]
                ):
                chunk_filtered = chunk[all_feature].copy() # 使用 .copy() 避免 SettingWithCopyWarning



                # **关键点：在导入时生成 group_id 并添加到 DataFrame 中**
                chunk_filtered['group_id'] = chunk_filtered[feature_for_group].astype(str).agg('_'.join, axis=1)

                # 归一化处理，保留6位小数
                chunk_filtered['feature4'] = np.round(np.float64((chunk_filtered['feature4'] - 3e7) / (25e8 - 3e7)), 6)
                chunk_filtered['feature5'] = np.round(np.float64(chunk_filtered['feature5'] / 655e6), 6)
                chunk_filtered['feature6'] = np.round(np.float64((chunk_filtered['feature6'] - (-100)) / (100 - (-100))), 6)

                # 将处理后的块追加到数据库表中
                chunk_filtered.to_sql(ALL_TABLE_NAME, conn_all, if_exists='append', index=False)

            print(f"  已导入文件: {file_name} ({i+1}/{len(csv_files)})")
        except Exception as e:
            print(f"导入文件 '{file_name}' 时出错: {e}")
            conn_all.close()
            exit()
    print(f"训练集数据存储在: {ALL_DB_PATH} (表: {ALL_TABLE_NAME})")
    print("--- 步骤 1 完成: 所有数据已导入主数据库 ---")





    print("\n--- 步骤 2: 从主数据库获取唯一组ID并进行划分 ---")

    # 从数据库中获取所有唯一的组ID
    try:
        # 使用 DISTINCT 关键字只获取唯一的 group_id
        cursor_all.execute(f"SELECT DISTINCT group_id FROM {ALL_TABLE_NAME}")
        all_unique_group_ids = [row[0] for row in cursor_all.fetchall()]
        print(f"从数据库中获取到 {len(all_unique_group_ids)} 个唯一的特征组ID。")
    except Exception as e:
        print(f"获取组ID时出错: {e}")
        conn_all.close()
        exit()

    # 将唯一的组ID划分为训练组ID和测试组ID
    # 在这里，每个 group_id 都是一个独立的单元，因此可以使用标准的 train_test_split。
    train_group_ids, test_group_ids = train_test_split(
        all_unique_group_ids,
        test_size=0.2, # 20% 的组将用于测试集，可根据你的需求调整
        random_state=SEED # 固定随机种子，确保每次运行划分结果一致
    )

    print(f"训练组ID数量: {len(train_group_ids)}")
    print(f"测试组ID数量: {len(test_group_ids)}")

    print("--- 步骤 2 完成: 组ID已划分 ---")






    print("\n--- 步骤 3: 从主数据库提取数据并写入独立的训练/测试数据库 ---")


    # --- 连接或创建训练集数据库 ---
    # 如果文件存在且非空，先删除以确保全新开始（可选，根据需求决定是否覆盖）
    if os.path.exists(TRAIN_DB_PATH) and os.path.getsize(TRAIN_DB_PATH) > 0:
        print(f"警告：'{TRAIN_DB_PATH}' 已存在且非空，将被覆盖。")
        try:
            os.remove(TRAIN_DB_PATH)
            print(f"已删除旧训练数据库文件: {TRAIN_DB_PATH}")
        except Exception as e:
            print(f"删除训练数据库文件时出错: {e}")
            conn_all.close()
            exit()
    conn_train = sqlite3.connect(TRAIN_DB_PATH)
    cursor_train = conn_train.cursor()
    cursor_train.execute(CREATE_TRAIN_TABLE_SQL)
    conn_train.commit()

    # --- 连接或创建测试集数据库 ---
    if os.path.exists(TEST_DB_PATH) and os.path.getsize(TEST_DB_PATH) > 0:
        print(f"警告：'{TEST_DB_PATH}' 已存在且非空，将被覆盖。")
        try:
            os.remove(TEST_DB_PATH)
            print(f"已删除旧测试数据库文件: {TEST_DB_PATH}")
        except Exception as e:
            print(f"删除测试数据库文件时出错: {e}")
            conn_all.close()
            conn_train.close()
            exit()
    conn_test = sqlite3.connect(TEST_DB_PATH)
    cursor_test = conn_test.cursor()
    cursor_test.execute(CREATE_TEST_TABLE_SQL)
    conn_test.commit()

    # --- 提取和导入训练集数据 ---
    print("\n开始提取并写入训练集数据库...")
    # SQLite 的 IN 子句对列表长度有限制（通常是几万个）。
    # 对于非常大的 group_id 列表，我们需要分批构建 SQL 查询。
    for i in range(0, len(train_group_ids), CHUNK_SIZE_QUERY):
        batch_group_ids = train_group_ids[i:i + CHUNK_SIZE_QUERY]
        # 构建安全的 SQL IN 子句字符串，注意引号
        batch_group_ids_str = "', '".join(batch_group_ids)
        query_train_batch = f"SELECT * FROM {ALL_TABLE_NAME} WHERE group_id IN ('{batch_group_ids_str}')"

        # 使用 pd.read_sql_query 一次性从数据库中加载当前批次的数据到 DataFrame
        df_train_batch = pd.read_sql_query(query_train_batch, conn_all)
        # 仅选择最终需要的列，并写入训练数据库
        df_train_batch[feature_and_id].to_sql(TRAIN_TABLE_NAME, conn_train, if_exists='append', index=False)
        print(f"  训练集导入进度: 已处理 {i + len(batch_group_ids)}/{len(train_group_ids)} 组ID。")

    # --- 提取和导入测试集数据 ---
    print("\n开始提取并写入测试集数据库...")
    for i in range(0, len(test_group_ids), CHUNK_SIZE_QUERY):
        batch_group_ids = test_group_ids[i:i + CHUNK_SIZE_QUERY]
        batch_group_ids_str = "', '".join(batch_group_ids)
        query_test_batch = f"SELECT * FROM {ALL_TABLE_NAME} WHERE group_id IN ('{batch_group_ids_str}')"

        df_test_batch = pd.read_sql_query(query_test_batch, conn_all)
        # 仅选择最终需要的列，并写入测试数据库
        df_test_batch[feature_and_id].to_sql(TEST_TABLE_NAME, conn_test, if_exists='append', index=False)
        print(f"  测试集导入进度: 已处理 {i + len(batch_group_ids)}/{len(test_group_ids)} 组ID。")

    # --- 创建复合索引 --- 
    create_train_idx = '''
    CREATE INDEX IF NOT EXISTS train_idx 
    ON train_table (
        "feature1", "feature2", "feature3", "feature4", "feature5", "feature6",
        "action"
    );
    '''
    cursor_train.execute(create_train_idx)
    conn_train.commit()

    create_test_idx = '''
    CREATE INDEX IF NOT EXISTS test_idx 
    ON test_table (
        "feature1", "feature2", "feature3", "feature4", "feature5", "feature6",
        "action"
    );
    '''
    cursor_test.execute(create_test_idx)
    conn_test.commit()

    # --- 关闭所有数据库连接 ---
    conn_all.close()
    conn_train.close()
    conn_test.close()

    print(f"训练集数据存储在: {TRAIN_DB_PATH} (表: {TRAIN_TABLE_NAME})")
    print(f"测试集数据存储在: {TEST_DB_PATH} (表: {TEST_TABLE_NAME})")
    print("--- 步骤 3 完成: 数据已成功划分并存入独立的训练和测试数据库 ---")

    print("\n--- 数据处理完成 ---")


    # 打印数据库的信息

    db_info = [
        ("主数据库", 'data/all_data.db', 'all_table'),
        ("训练集数据库", 'data/train_data.db', 'train_table'),
        ("测试集数据库", 'data/test_data.db', 'test_table')
    ]

    for db_name, db_path, table_name in db_info:
        print(f"\n=== {db_name} ===")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        # 打印表结构
        print("表结构：")
        cursor.execute(f"PRAGMA table_info({table_name});")
        for row in cursor.fetchall():
            print(row)
        # 打印总行数
        cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
        total_rows = cursor.fetchone()[0]
        print(f"总行数: {total_rows}")
        # 打印前10行数据
        print("前10行数据：")
        df = pd.read_sql_query(f"SELECT * FROM {table_name} LIMIT 10;", conn)
        print(df)
        conn.close()

    if TRAIN_DB_PATH and TEST_DB_PATH:
        return TRAIN_DB_PATH, TEST_DB_PATH
    else:
        return None, None