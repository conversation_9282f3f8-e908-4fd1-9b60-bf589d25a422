# backend/eval.py
import threading
import time
import random

class EvaluationManager:
    """
    评估管理类，封装所有评估相关的后端逻辑和线程管理。
    """
    def __init__(self):
        self._evaluation_thread = None
    
    def start_evaluation(self, model_path, data_path, result_callback):
        """
        启动模型评估过程。

        Args:
            model_path (str): 模型文件路径。
            data_path (str): 评估数据文件路径。
            result_callback (callable): 评估完成后用于更新UI的回调函数。
        """
        if self._evaluation_thread and self._evaluation_thread.is_alive():
            print("评估已在运行中。")
            return
        
        # 通知UI“正在评估中...”
        result_callback("评估中...")
        
        # 在新线程中运行评估核心逻辑
        self._evaluation_thread = threading.Thread(
            target=self._run_evaluation_core,
            args=(model_path, data_path, result_callback)
        )
        self._evaluation_thread.daemon = True
        self._evaluation_thread.start()
        print("评估已启动。")
        
    def _run_evaluation_core(self, model_path, data_path, result_callback):
        """
        评估核心逻辑，模拟评估过程。
        """
        print(f"后端：开始评估模型文件: {model_path}，使用数据文件: {data_path}")
        try:
            time.sleep(2) # 模拟评估耗时
            accuracy = round(random.uniform(85, 99), 2)
            result = f"正确率: {accuracy}%"
            print("后端：评估完成！")
            
            # 调用前端的回调函数来更新UI结果
            result_callback(result)
            
        except Exception as e:
            result_callback(f"评估出错: {e}")